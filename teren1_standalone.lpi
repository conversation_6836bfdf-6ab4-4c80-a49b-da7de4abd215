<?xml version="1.0" encoding="UTF-8"?>
<CONFIG>
  <ProjectOptions>
    <Version Value="12"/>
    <General>
      <Flags>
        <MainUnitHasUsesSectionForAllUnits Value="False"/>
        <MainUnitHasCreateFormStatements Value="False"/>
        <MainUnitHasTitleStatement Value="False"/>
        <MainUnitHasScaledStatement Value="False"/>
        <CompatibilityMode Value="True"/>
      </Flags>
      <SessionStorage Value="InProjectDir"/>
      <Title Value="teren1"/>
    </General>
    <BuildModes Count="1">
      <Item1 Name="default" Default="True"/>
    </BuildModes>
    <PublishOptions>
      <Version Value="2"/>
    </PublishOptions>
    <RunParams>
      <FormatVersion Value="2"/>
    </RunParams>
    <RequiredPackages Count="2">
      <Item1>
        <PackageName Value="castle_window"/>
      </Item1>
      <Item2>
        <PackageName Value="castle_base"/>
      </Item2>
    </RequiredPackages>
    <Units Count="4">
      <Unit0>
        <Filename Value="teren1_standalone.dpr"/>
        <IsPartOfProject Value="True"/>
      </Unit0>
      <Unit1>
        <Filename Value="castleautogenerated.pas"/>
        <IsPartOfProject Value="True"/>
      </Unit1>
      <Unit2>
        <Filename Value="code/gameinitialize.pas"/>
        <IsPartOfProject Value="True"/>
      </Unit2>
      <Unit3>
        <Filename Value="code/gameviewmain.pas"/>
        <IsPartOfProject Value="True"/>
        <UnitName Value="GameViewMain"/>
      </Unit3>
    </Units>
  </ProjectOptions>
  <CompilerOptions>
    <Version Value="11"/>
    <Target>
      <Filename Value="teren1"/>
    </Target>
    <SearchPaths>
      <IncludeFiles Value="code"/>
      <OtherUnitFiles Value="code"/>
      <UnitOutputDirectory Value="castle-engine-output/standalone/lazarus-lib/$(TargetCPU)-$(TargetOS)"/>
    </SearchPaths>
    <Parsing>
      <SyntaxOptions>
        <IncludeAssertionCode Value="True"/>
      </SyntaxOptions>
    </Parsing>
    <CodeGeneration>
      <Checks>
        <IOChecks Value="True"/>
        <RangeChecks Value="True"/>
        <OverflowChecks Value="True"/>
      </Checks>
    </CodeGeneration>
    <Linking>
      <Debugging>
        <DebugInfoType Value="dsDwarf2Set"/>
      </Debugging>
    </Linking>
    <Other>
      <Verbosity>
        <ShowHints Value="False"/>
      </Verbosity>
    </Other>
  </CompilerOptions>
  <Debugging>
    <Exceptions Count="3">
      <Item1>
        <Name Value="EAbort"/>
      </Item1>
      <Item2>
        <Name Value="ECodetoolError"/>
      </Item2>
      <Item3>
        <Name Value="EFOpenError"/>
      </Item3>
    </Exceptions>
  </Debugging>
</CONFIG>
