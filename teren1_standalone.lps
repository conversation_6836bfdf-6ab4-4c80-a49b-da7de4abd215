<?xml version="1.0" encoding="UTF-8"?>
<CONFIG>
  <ProjectSession>
    <Version Value="12"/>
    <Units Count="69">
      <Unit0>
        <Filename Value="teren1_standalone.dpr"/>
        <IsPartOfProject Value="True"/>
        <TopLine Value="25"/>
        <UsageCount Value="124"/>
        <Loaded Value="True"/>
      </Unit0>
      <Unit1>
        <Filename Value="castleautogenerated.pas"/>
        <IsPartOfProject Value="True"/>
        <UsageCount Value="124"/>
      </Unit1>
      <Unit2>
        <Filename Value="code/gameinitialize.pas"/>
        <IsPartOfProject Value="True"/>
        <UsageCount Value="124"/>
      </Unit2>
      <Unit3>
        <Filename Value="code/gameviewmain.pas"/>
        <IsPartOfProject Value="True"/>
        <UnitName Value="GameViewMain"/>
        <IsVisibleTab Value="True"/>
        <EditorIndex Value="2"/>
        <TopLine Value="173"/>
        <CursorPos X="60" Y="189"/>
        <UsageCount Value="124"/>
        <Loaded Value="True"/>
      </Unit3>
      <Unit4>
        <Filename Value="../../../castle-engine/src/files/castlecomponentserialize.pas"/>
        <UnitName Value="CastleComponentSerialize"/>
        <EditorIndex Value="1"/>
        <TopLine Value="495"/>
        <CursorPos Y="512"/>
        <UsageCount Value="61"/>
        <Loaded Value="True"/>
      </Unit4>
      <Unit5>
        <Filename Value="../../../castle-engine/src/scene/castleterrain.pas"/>
        <UnitName Value="CastleTerrain"/>
        <EditorIndex Value="4"/>
        <TopLine Value="2516"/>
        <CursorPos Y="2545"/>
        <UsageCount Value="61"/>
        <Loaded Value="True"/>
      </Unit5>
      <Unit6>
        <Filename Value="../../../castle-engine/src/scene/castleviewport.pas"/>
        <UnitName Value="CastleViewport"/>
        <EditorIndex Value="8"/>
        <TopLine Value="595"/>
        <CursorPos X="14" Y="612"/>
        <UsageCount Value="36"/>
        <Loaded Value="True"/>
      </Unit6>
      <Unit7>
        <Filename Value="../../../castle-engine/src/transform/castletransform_initial_types.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="334"/>
        <CursorPos X="23" Y="349"/>
        <UsageCount Value="6"/>
      </Unit7>
      <Unit8>
        <Filename Value="../../../castle-engine/src/ui/castlecontrols_label.inc"/>
        <EditorIndex Value="-1"/>
        <CursorPos X="3" Y="22"/>
        <UsageCount Value="6"/>
      </Unit8>
      <Unit9>
        <Filename Value="../../../castle-engine/src/images/castleimages.pas"/>
        <UnitName Value="CastleImages"/>
        <EditorIndex Value="14"/>
        <TopLine Value="81"/>
        <CursorPos X="24" Y="97"/>
        <UsageCount Value="51"/>
        <Loaded Value="True"/>
      </Unit9>
      <Unit10>
        <Filename Value="../../../fixes/fpcsrc/packages/rtl-generics/src/generics.defaults.pas"/>
        <UnitName Value="Generics.Defaults"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="856"/>
        <CursorPos X="14" Y="874"/>
        <UsageCount Value="5"/>
      </Unit10>
      <Unit11>
        <Filename Value="../../../castle-engine/src/ui/castleuicontrols_container.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="1505"/>
        <CursorPos X="24" Y="1521"/>
        <UsageCount Value="37"/>
      </Unit11>
      <Unit12>
        <Filename Value="../../../castle-engine/src/ui/castlekeysmouse.pas"/>
        <UnitName Value="CastleKeysMouse"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="363"/>
        <CursorPos X="3" Y="379"/>
        <UsageCount Value="5"/>
      </Unit12>
      <Unit13>
        <Filename Value="../../../castle-engine/src/base/castleutils_struct_list.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="92"/>
        <CursorPos X="14" Y="107"/>
        <UsageCount Value="12"/>
      </Unit13>
      <Unit14>
        <Filename Value="../../../castle-engine/src/transform/castletransform_transform.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="614"/>
        <CursorPos X="17" Y="629"/>
        <UsageCount Value="11"/>
      </Unit14>
      <Unit15>
        <Filename Value="../../../castle-engine/src/window/castlewindow.pas"/>
        <UnitName Value="CastleWindow"/>
        <EditorIndex Value="9"/>
        <TopLine Value="415"/>
        <CursorPos X="25" Y="437"/>
        <UsageCount Value="34"/>
        <Loaded Value="True"/>
      </Unit15>
      <Unit16>
        <Filename Value="../../../castle-engine/src/window/gtk/castlewindow_gtk.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="1660"/>
        <CursorPos Y="1676"/>
        <UsageCount Value="3"/>
      </Unit16>
      <Unit17>
        <Filename Value="../../../castle-engine/src/ui/castlecontrols_sliders.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="156"/>
        <CursorPos X="3" Y="156"/>
        <UsageCount Value="3"/>
      </Unit17>
      <Unit18>
        <Filename Value="../../../castle-engine/src/common_includes/castleconf.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="28"/>
        <CursorPos X="72" Y="50"/>
        <UsageCount Value="8"/>
      </Unit18>
      <Unit19>
        <Filename Value="../../../castle-engine/src/scene/glsl/generated-pascal/terrain.fs.inc"/>
        <EditorIndex Value="-1"/>
        <UsageCount Value="7"/>
      </Unit19>
      <Unit20>
        <Filename Value="../../../castle-engine/src/scene/glsl/generated-pascal/terrain.vs.inc"/>
        <EditorIndex Value="-1"/>
        <UsageCount Value="7"/>
      </Unit20>
      <Unit21>
        <Filename Value="../../../castle-engine/src/scene/x3d/x3dnodes_castle.inc"/>
        <EditorIndex Value="29"/>
        <TopLine Value="298"/>
        <CursorPos X="56" Y="315"/>
        <UsageCount Value="48"/>
        <Loaded Value="True"/>
      </Unit21>
      <Unit22>
        <Filename Value="../../../castle-engine/src/transform/castletransform_physics.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="122"/>
        <CursorPos X="3" Y="139"/>
        <UsageCount Value="2"/>
      </Unit22>
      <Unit23>
        <Filename Value="../../../castle-engine/src/scene/x3d/x3dnodes_standard_geometry3d.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="80"/>
        <CursorPos X="52" Y="96"/>
        <UsageCount Value="5"/>
      </Unit23>
      <Unit24>
        <Filename Value="../../../castle-engine/src/scene/glsl/generated-pascal/terrain2.vs.inc"/>
        <EditorIndex Value="-1"/>
        <UsageCount Value="3"/>
      </Unit24>
      <Unit25>
        <Filename Value="../../../castle-engine/src/base_rendering/castleglshaders.pas"/>
        <UnitName Value="CastleGLShaders"/>
        <EditorIndex Value="19"/>
        <TopLine Value="116"/>
        <CursorPos X="3" Y="127"/>
        <UsageCount Value="38"/>
        <Loaded Value="True"/>
      </Unit25>
      <Unit26>
        <Filename Value="../../../castle-engine/src/scene/x3d/x3dnodes_standard_texturing.inc"/>
        <EditorIndex Value="26"/>
        <TopLine Value="399"/>
        <CursorPos X="18" Y="419"/>
        <UsageCount Value="49"/>
        <Loaded Value="True"/>
      </Unit26>
      <Unit27>
        <Filename Value="../../../castle-engine/src/scene/x3d/auto_generated_node_helpers/x3dnodes_imagetexture.inc"/>
        <EditorIndex Value="38"/>
        <TopLine Value="20"/>
        <CursorPos Y="46"/>
        <UsageCount Value="45"/>
        <Loaded Value="True"/>
      </Unit27>
      <Unit28>
        <Filename Value="../../../castle-engine/src/scene/x3d/auto_generated_node_helpers/x3dnodes_pixeltexture.inc"/>
        <EditorIndex Value="36"/>
        <TopLine Value="21"/>
        <CursorPos X="33" Y="34"/>
        <UsageCount Value="45"/>
        <Loaded Value="True"/>
      </Unit28>
      <Unit29>
        <Filename Value="../../../castle-engine/src/scene/x3d/castlefields_x3dsinglefield_descendants.inc"/>
        <EditorIndex Value="37"/>
        <TopLine Value="278"/>
        <CursorPos X="3" Y="268"/>
        <UsageCount Value="45"/>
        <Loaded Value="True"/>
      </Unit29>
      <Unit30>
        <Filename Value="../../../castle-engine/src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dtexture2dnode.inc"/>
        <EditorIndex Value="35"/>
        <TopLine Value="37"/>
        <CursorPos X="14" Y="54"/>
        <UsageCount Value="45"/>
        <Loaded Value="True"/>
      </Unit30>
      <Unit31>
        <Filename Value="../../../castle-engine/src/scene/castleinternalrenderer_texture.inc"/>
        <EditorIndex Value="30"/>
        <TopLine Value="225"/>
        <CursorPos X="15" Y="253"/>
        <UsageCount Value="45"/>
        <Loaded Value="True"/>
      </Unit31>
      <Unit32>
        <Filename Value="../../../castle-engine/src/scene/castleinternalrenderer_resource.inc"/>
        <EditorIndex Value="28"/>
        <TopLine Value="19"/>
        <CursorPos X="29" Y="29"/>
        <UsageCount Value="45"/>
        <Loaded Value="True"/>
      </Unit32>
      <Unit33>
        <Filename Value="../../../castle-engine/src/scene/castleinternalrenderer_renderer.inc"/>
        <EditorIndex Value="34"/>
        <TopLine Value="1264"/>
        <CursorPos X="22" Y="1282"/>
        <UsageCount Value="45"/>
        <Loaded Value="True"/>
      </Unit33>
      <Unit34>
        <Filename Value="../../../castle-engine/src/scene/castlescene.pas"/>
        <UnitName Value="CastleScene"/>
        <EditorIndex Value="32"/>
        <TopLine Value="1436"/>
        <CursorPos X="42" Y="1462"/>
        <UsageCount Value="45"/>
        <Loaded Value="True"/>
      </Unit34>
      <Unit35>
        <Filename Value="../../../castle-engine/src/scene/x3d/x3dnodes_instantreality.inc"/>
        <EditorIndex Value="33"/>
        <TopLine Value="74"/>
        <CursorPos X="3" Y="90"/>
        <UsageCount Value="45"/>
        <Loaded Value="True"/>
      </Unit35>
      <Unit36>
        <Filename Value="../../../castle-engine/src/base_rendering/castleglimages_rendertotexture.inc"/>
        <EditorIndex Value="22"/>
        <TopLine Value="596"/>
        <CursorPos Y="614"/>
        <UsageCount Value="43"/>
        <Loaded Value="True"/>
      </Unit36>
      <Unit37>
        <Filename Value="../../../castle-engine/src/base_rendering/castleglutils_types.inc"/>
        <EditorIndex Value="24"/>
        <TopLine Value="9"/>
        <CursorPos X="22" Y="32"/>
        <UsageCount Value="43"/>
        <Loaded Value="True"/>
      </Unit37>
      <Unit38>
        <Filename Value="../../../castle-engine/src/base_rendering/dglopengl/castlegl.pas"/>
        <UnitName Value="CastleGL"/>
        <EditorIndex Value="25"/>
        <TopLine Value="233"/>
        <CursorPos X="3" Y="250"/>
        <UsageCount Value="43"/>
        <Loaded Value="True"/>
      </Unit38>
      <Unit39>
        <Filename Value="../../../castle-engine/src/base_rendering/castleglimages_drawableimage.inc"/>
        <EditorIndex Value="11"/>
        <TopLine Value="60"/>
        <CursorPos X="3" Y="65"/>
        <UsageCount Value="42"/>
        <Loaded Value="True"/>
      </Unit39>
      <Unit40>
        <Filename Value="../../../castle-engine/src/base/castlerenderoptions_renderoptions.inc"/>
        <EditorIndex Value="31"/>
        <TopLine Value="26"/>
        <CursorPos X="3" Y="26"/>
        <UsageCount Value="42"/>
        <Loaded Value="True"/>
      </Unit40>
      <Unit41>
        <Filename Value="../../../castle-engine/src/files/castledownload_synchronous.inc"/>
        <EditorIndex Value="17"/>
        <TopLine Value="136"/>
        <CursorPos Y="154"/>
        <UsageCount Value="42"/>
        <Loaded Value="True"/>
      </Unit41>
      <Unit42>
        <Filename Value="../../../castle-engine/src/base_rendering/castleglimages_load_2d.inc"/>
        <EditorIndex Value="13"/>
        <TopLine Value="554"/>
        <CursorPos X="3" Y="559"/>
        <UsageCount Value="39"/>
        <Loaded Value="True"/>
      </Unit42>
      <Unit43>
        <Filename Value="../../../castle-engine/src/base_rendering/castleglimages_texturememoryprofiler.inc"/>
        <EditorIndex Value="23"/>
        <TopLine Value="34"/>
        <CursorPos X="41" Y="51"/>
        <UsageCount Value="39"/>
        <Loaded Value="True"/>
      </Unit43>
      <Unit44>
        <Filename Value="../../../castle-engine/src/scene/castleinternalrenderer_surfacetextures.inc"/>
        <EditorIndex Value="-1"/>
        <CursorPos X="53" Y="30"/>
        <UsageCount Value="5"/>
      </Unit44>
      <Unit45>
        <Filename Value="../../../castle-engine/src/scene/x3d/x3dnodes_x3dnode.inc"/>
        <EditorIndex Value="27"/>
        <TopLine Value="3273"/>
        <CursorPos Y="3291"/>
        <UsageCount Value="39"/>
        <Loaded Value="True"/>
      </Unit45>
      <Unit46>
        <Filename Value="../../../castle-engine/src/base_rendering/castleglimages_miscellaneous.inc"/>
        <EditorIndex Value="21"/>
        <TopLine Value="36"/>
        <CursorPos X="25" Y="52"/>
        <UsageCount Value="38"/>
        <Loaded Value="True"/>
      </Unit46>
      <Unit47>
        <Filename Value="../../../castle-engine/src/base_rendering/castlerendercontext.pas"/>
        <UnitName Value="CastleRenderContext"/>
        <EditorIndex Value="20"/>
        <TopLine Value="314"/>
        <CursorPos X="3" Y="331"/>
        <UsageCount Value="38"/>
        <Loaded Value="True"/>
      </Unit47>
      <Unit48>
        <Filename Value="../../../castle-engine/src/base_rendering/castleglutils_vertex_array_object.inc"/>
        <EditorIndex Value="18"/>
        <CursorPos X="3" Y="33"/>
        <UsageCount Value="38"/>
        <Loaded Value="True"/>
      </Unit48>
      <Unit49>
        <Filename Value="../../../castle-engine/src/base_rendering/castleinternalglutils_errors.inc"/>
        <EditorIndex Value="3"/>
        <TopLine Value="104"/>
        <CursorPos Y="134"/>
        <UsageCount Value="31"/>
        <Loaded Value="True"/>
      </Unit49>
      <Unit50>
        <Filename Value="../../../castle-engine/src/scene/x3d/x3dnodes_standard_rendering.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="401"/>
        <CursorPos X="14" Y="418"/>
        <UsageCount Value="30"/>
      </Unit50>
      <Unit51>
        <Filename Value="../../../fixes/fpcsrc/rtl/objpas/classes/classesh.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="1956"/>
        <CursorPos X="15" Y="1971"/>
        <UsageCount Value="6"/>
      </Unit51>
      <Unit52>
        <Filename Value="../../../castle-engine/src/scene/castlescreeneffects.pas"/>
        <UnitName Value="CastleScreenEffects"/>
        <EditorIndex Value="10"/>
        <TopLine Value="173"/>
        <CursorPos X="15" Y="187"/>
        <UsageCount Value="35"/>
        <Loaded Value="True"/>
      </Unit52>
      <Unit53>
        <Filename Value="../../../castle-engine/src/ui/castleuicontrols_userinterface.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="1120"/>
        <CursorPos X="14" Y="1133"/>
        <UsageCount Value="28"/>
      </Unit53>
      <Unit54>
        <Filename Value="../../../castle-engine/src/scene/x3d/auto_generated_node_helpers/x3dnodes_appearance.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="19"/>
        <CursorPos X="15" Y="36"/>
        <UsageCount Value="27"/>
      </Unit54>
      <Unit55>
        <Filename Value="../../../castle-engine/src/deprecated_units/castleglcontainer.pas"/>
        <UnitName Value="CastleGLContainer"/>
        <EditorIndex Value="16"/>
        <CursorPos X="18" Y="27"/>
        <UsageCount Value="34"/>
        <Loaded Value="True"/>
      </Unit55>
      <Unit56>
        <Filename Value="../../../castle-engine/src/ui/castleuicontrols_internalchildrencontrols.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="19"/>
        <CursorPos X="46" Y="43"/>
        <UsageCount Value="27"/>
      </Unit56>
      <Unit57>
        <Filename Value="../../../castle-engine/src/base/castlecolors.pas"/>
        <UnitName Value="CastleColors"/>
        <EditorIndex Value="-1"/>
        <CursorPos X="3" Y="27"/>
        <UsageCount Value="27"/>
      </Unit57>
      <Unit58>
        <Filename Value="../../../castle-engine/src/scene/castlescene_editorgizmo.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="71"/>
        <CursorPos X="31" Y="89"/>
        <UsageCount Value="27"/>
      </Unit58>
      <Unit59>
        <Filename Value="../../../castle-engine/src/scene/castlescene_imagetransform.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="148"/>
        <CursorPos X="44" Y="174"/>
        <UsageCount Value="27"/>
      </Unit59>
      <Unit60>
        <Filename Value="../../../castle-engine/src/base/castleprojection.pas"/>
        <UnitName Value="CastleProjection"/>
        <EditorIndex Value="-1"/>
        <CursorPos X="26" Y="27"/>
        <UsageCount Value="6"/>
      </Unit60>
      <Unit61>
        <Filename Value="../../../castle-engine/src/transform/castletransform_camera.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="104"/>
        <CursorPos X="14" Y="137"/>
        <UsageCount Value="25"/>
      </Unit61>
      <Unit62>
        <Filename Value="../../../castle-engine/src/ui/castleuicontrols_initial_types.inc"/>
        <EditorIndex Value="15"/>
        <TopLine Value="44"/>
        <CursorPos X="3" Y="45"/>
        <UsageCount Value="31"/>
        <Loaded Value="True"/>
      </Unit62>
      <Unit63>
        <Filename Value="../../../castle-engine/src/scene/x3d/auto_generated_node_helpers/x3dnodes_indexedtriangleset.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="13"/>
        <UsageCount Value="7"/>
      </Unit63>
      <Unit64>
        <Filename Value="../../../castle-engine/src/scene/x3d/castlefields_x3dfield.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="755"/>
        <CursorPos X="41" Y="777"/>
        <UsageCount Value="23"/>
      </Unit64>
      <Unit65>
        <Filename Value="../../../castle-engine/src/scene/x3d/x3dnodes_mfnode.inc"/>
        <EditorIndex Value="6"/>
        <TopLine Value="338"/>
        <CursorPos Y="366"/>
        <UsageCount Value="30"/>
        <Loaded Value="True"/>
      </Unit65>
      <Unit66>
        <Filename Value="../../../castle-engine/src/scene/x3d/x3dnodes_standard_grouping.inc"/>
        <EditorIndex Value="7"/>
        <TopLine Value="587"/>
        <CursorPos Y="605"/>
        <UsageCount Value="30"/>
        <Loaded Value="True"/>
      </Unit66>
      <Unit67>
        <Filename Value="../../../castle-engine/src/scene/x3d/auto_generated_node_helpers/x3dnodes_textureproperties.inc"/>
        <EditorIndex Value="5"/>
        <TopLine Value="57"/>
        <CursorPos X="14" Y="74"/>
        <UsageCount Value="30"/>
        <Loaded Value="True"/>
      </Unit67>
      <Unit68>
        <Filename Value="../../../castle-engine/src/base/castlerenderoptions_globals.inc"/>
        <EditorIndex Value="12"/>
        <TopLine Value="299"/>
        <CursorPos X="3" Y="308"/>
        <UsageCount Value="16"/>
        <Loaded Value="True"/>
      </Unit68>
    </Units>
    <JumpHistory Count="30" HistoryIndex="29">
      <Position1>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="55" Column="24" TopLine="38"/>
      </Position1>
      <Position2>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="59" Column="21" TopLine="52"/>
      </Position2>
      <Position3>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="79" Column="26" TopLine="72"/>
      </Position3>
      <Position4>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="64" Column="18" TopLine="42"/>
      </Position4>
      <Position5>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="135" Column="3" TopLine="132"/>
      </Position5>
      <Position6>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="65" Column="17" TopLine="48"/>
      </Position6>
      <Position7>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="141" Column="3" TopLine="138"/>
      </Position7>
      <Position8>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="64" Column="17" TopLine="49"/>
      </Position8>
      <Position9>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="99" Column="89" TopLine="81"/>
      </Position9>
      <Position10>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="60" Column="41" TopLine="43"/>
      </Position10>
      <Position11>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="80" Column="32" TopLine="78"/>
      </Position11>
      <Position12>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="59" Column="17" TopLine="42"/>
      </Position12>
      <Position13>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="94" Column="3" TopLine="91"/>
      </Position13>
      <Position14>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="60" Column="17" TopLine="43"/>
      </Position14>
      <Position15>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="101" Column="3" TopLine="98"/>
      </Position15>
      <Position16>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="66" Column="16" TopLine="42"/>
      </Position16>
      <Position17>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="154" Column="3" TopLine="146"/>
      </Position17>
      <Position18>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="59" Column="16" TopLine="50"/>
      </Position18>
      <Position19>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="97" TopLine="91"/>
      </Position19>
      <Position20>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="87" Column="25" TopLine="69"/>
      </Position20>
      <Position21>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="94" Column="23" TopLine="90"/>
      </Position21>
      <Position22>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="150" Column="19" TopLine="133"/>
      </Position22>
      <Position23>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="57" Column="5" TopLine="41"/>
      </Position23>
      <Position24>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="65" Column="19" TopLine="37"/>
      </Position24>
      <Position25>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="168" TopLine="144"/>
      </Position25>
      <Position26>
        <Filename Value="code/gameviewmain.pas"/>
        <Caret Line="190" Column="45" TopLine="170"/>
      </Position26>
      <Position27>
        <Filename Value="../../../castle-engine/src/scene/castleterrain.pas"/>
        <Caret Line="729" Column="16" TopLine="714"/>
      </Position27>
      <Position28>
        <Filename Value="../../../castle-engine/src/scene/castleterrain.pas"/>
        <Caret Line="2486" Column="5" TopLine="2485"/>
      </Position28>
      <Position29>
        <Filename Value="../../../castle-engine/src/scene/castleterrain.pas"/>
        <Caret Line="729" Column="28" TopLine="727"/>
      </Position29>
      <Position30>
        <Filename Value="../../../castle-engine/src/scene/castleterrain.pas"/>
        <Caret Line="2545" TopLine="2516"/>
      </Position30>
    </JumpHistory>
    <RunParams>
      <FormatVersion Value="2"/>
      <Modes ActiveMode=""/>
    </RunParams>
  </ProjectSession>
</CONFIG>
